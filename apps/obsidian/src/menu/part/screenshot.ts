import type { PlayerContext } from "../def";
import { Notice, type Menu } from "obsidian";
import {
  copyBlob,
  extractAndCopyTextFromScreenshot,
  saveScreenshotAsCoverFromMenu,
} from "@/screenshot/actions";
import { isVideoProvider } from "@vidstack/react";

export function screenshotMenu(
  menu: Menu,
  ctx: Pick<PlayerContext, "captureScreenshot" | "player" | "plugin" | "src">,
) {
  if (!ctx.captureScreenshot) return;
  const capture = ctx.captureScreenshot;
  if (ctx.player && !isVideoProvider(ctx.player.provider)) return;
  menu.addItem((item) =>
    item
      .setTitle("Copy Screenshot")
      .setSection("action")
      .setIcon("focus")
      .onClick(async () => {
        try {
          const screenshot = await capture({ format: "image/png" });
          await copyBlob(screenshot.blob);
          new Notice("Screenshot copied to clipboard");
        } catch (e) {
          new Notice("Failed to copy screenshot, see console for details");
          console.error("Failed to copy screenshot", e);
        }
      }),
  );
  menu.addItem((item) =>
    item
      .setTitle("Extract text from screenshot")
      .setSection("action")
      .setIcon("scan-text")
      .onClick(() => {
        extractAndCopyTextFromScreenshot({
          plugin: ctx.plugin,
          getMediaInfo: () => ctx.src,
          getPlayer: () => ctx.player,
          captureScreenshot: ctx.captureScreenshot,
        });
      }),
  );
  menu.addItem((item) => {
    const menuItem = item
      .setTitle("Set cover image")
      .setSection("action")
      .setIcon("image");

    // Check if functionality is supported
    const isSupported =
      ctx.captureScreenshot &&
      ctx.player &&
      isVideoProvider(ctx.player.provider);

    if (!isSupported) {
      menuItem.setDisabled(true);
    }

    menuItem.onClick(async () => {
      if (!isSupported || !ctx.captureScreenshot) return;

      await saveScreenshotAsCoverFromMenu(
        {
          plugin: ctx.plugin,
          getMediaInfo: () => ctx.src,
          getPlayer: () => ctx.player,
          captureScreenshot: ctx.captureScreenshot,
        },
        { clip: true },
      );
    });
  });
}
